"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.default = _applyDecoratedDescriptor;
function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) {
    var desc1 = {};
    Object['ke' + 'ys'](descriptor).forEach(function(key) {
        desc1[key] = descriptor[key];
    });
    desc1.enumerable = !!desc1.enumerable;
    desc1.configurable = !!desc1.configurable;
    if ('value' in desc1 || desc1.initializer) {
        desc1.writable = true;
    }
    desc1 = decorators.slice().reverse().reduce(function(desc, decorator) {
        return decorator ? decorator(target, property, desc) || desc : desc;
    }, desc1);
    var hasAccessor = Object.prototype.hasOwnProperty.call(desc1, 'get') || Object.prototype.hasOwnProperty.call(desc1, 'set');
    if (context && desc1.initializer !== void 0 && !hasAccessor) {
        desc1.value = desc1.initializer ? desc1.initializer.call(context) : void 0;
        desc1.initializer = undefined;
    }
    if (hasAccessor) {
        delete desc1.writable;
        delete desc1.initializer;
        delete desc1.value;
    }
    if (desc1.initializer === void 0) {
        Object['define' + 'Property'](target, property, desc1);
        desc1 = null;
    }
    return desc1;
}
