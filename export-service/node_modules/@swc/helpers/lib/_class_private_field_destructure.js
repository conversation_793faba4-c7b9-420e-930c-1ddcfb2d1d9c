"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.default = _classPrivateFieldDestructureSet;
var _classExtractFieldDescriptor = _interopRequireDefault(require("./_class_extract_field_descriptor"));
var _classApplyDescriptorDestructure = _interopRequireDefault(require("./_class_apply_descriptor_destructure"));
function _classPrivateFieldDestructureSet(receiver, privateMap) {
    var descriptor = (0, _classExtractFieldDescriptor).default(receiver, privateMap, "set");
    return (0, _classApplyDescriptorDestructure).default(receiver, descriptor);
}
function _interopRequireDefault(obj) {
    return obj && obj.__esModule ? obj : {
        default: obj
    };
}
