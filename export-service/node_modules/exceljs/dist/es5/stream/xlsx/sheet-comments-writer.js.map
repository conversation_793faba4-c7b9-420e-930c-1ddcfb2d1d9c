{"version": 3, "file": "sheet-comments-writer.js", "names": ["XmlStream", "require", "RelType", "co<PERSON><PERSON><PERSON>", "CommentXform", "VmlShapeXform", "SheetCommentsWriter", "constructor", "worksheet", "sheetRelsWriter", "options", "id", "count", "_worksheet", "_workbook", "workbook", "_sheetRelsWriter", "commentsStream", "_commentsStream", "_openStream", "vmlStream", "_vmlStream", "_addRelationships", "commentRel", "Type", "Comments", "Target", "addRelationship", "vmlDrawingRel", "VmlDrawing", "vmlRelId", "_addCommentRefs", "commentRefs", "push", "commentName", "vmlDrawing", "_writeOpen", "write", "_writeComment", "comment", "index", "commentXform", "commentsXmlStream", "render", "xml", "vmlShapeXform", "vmlXmlStream", "_writeClose", "addComments", "comments", "length", "startedData", "for<PERSON>ach", "item", "refAddress", "decode<PERSON>ddress", "ref", "commit", "end", "module", "exports"], "sources": ["../../../../lib/stream/xlsx/sheet-comments-writer.js"], "sourcesContent": ["const XmlStream = require('../../utils/xml-stream');\nconst RelType = require('../../xlsx/rel-type');\nconst colCache = require('../../utils/col-cache');\nconst CommentXform = require('../../xlsx/xform/comment/comment-xform');\nconst VmlShapeXform = require('../../xlsx/xform/comment/vml-shape-xform');\n\nclass SheetCommentsWriter {\n  constructor(worksheet, sheetRelsWriter, options) {\n    // in a workbook, each sheet will have a number\n    this.id = options.id;\n    this.count = 0;\n    this._worksheet = worksheet;\n    this._workbook = options.workbook;\n    this._sheetRelsWriter = sheetRelsWriter;\n  }\n\n  get commentsStream() {\n    if (!this._commentsStream) {\n      // eslint-disable-next-line no-underscore-dangle\n      this._commentsStream = this._workbook._openStream(`/xl/comments${this.id}.xml`);\n    }\n    return this._commentsStream;\n  }\n\n  get vmlStream() {\n    if (!this._vmlStream) {\n      // eslint-disable-next-line no-underscore-dangle\n      this._vmlStream = this._workbook._openStream(`xl/drawings/vmlDrawing${this.id}.vml`);\n    }\n    return this._vmlStream;\n  }\n\n  _addRelationships() {\n    const commentRel = {\n      Type: RelType.Comments,\n      Target: `../comments${this.id}.xml`,\n    };\n    this._sheetRelsWriter.addRelationship(commentRel);\n\n    const vmlDrawingRel = {\n      Type: RelType.VmlDrawing,\n      Target: `../drawings/vmlDrawing${this.id}.vml`,\n    };\n    this.vmlRelId = this._sheetRelsWriter.addRelationship(vmlDrawingRel);\n  }\n\n  _addCommentRefs() {\n    this._workbook.commentRefs.push({\n      commentName: `comments${this.id}`,\n      vmlDrawing: `vmlDrawing${this.id}`,\n    });\n  }\n\n  _writeOpen() {\n    this.commentsStream.write(\n      '<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?>' +\n        '<comments xmlns=\"http://schemas.openxmlformats.org/spreadsheetml/2006/main\">' +\n        '<authors><author>Author</author></authors>' +\n        '<commentList>'\n    );\n    this.vmlStream.write(\n      '<?xml version=\"1.0\" encoding=\"UTF-8\"?>' +\n        '<xml xmlns:o=\"urn:schemas-microsoft-com:office:office\" xmlns:v=\"urn:schemas-microsoft-com:vml\" xmlns:x=\"urn:schemas-microsoft-com:office:excel\">' +\n        '<o:shapelayout v:ext=\"edit\">' +\n        '<o:idmap v:ext=\"edit\" data=\"1\" />' +\n        '</o:shapelayout>' +\n        '<v:shapetype id=\"_x0000_t202\" coordsize=\"21600,21600\" o:spt=\"202\" path=\"m,l,21600r21600,l21600,xe\">' +\n        '<v:stroke joinstyle=\"miter\" />' +\n        '<v:path gradientshapeok=\"t\" o:connecttype=\"rect\" />' +\n        '</v:shapetype>'\n    );\n  }\n\n  _writeComment(comment, index) {\n    const commentXform = new CommentXform();\n    const commentsXmlStream = new XmlStream();\n    commentXform.render(commentsXmlStream, comment);\n    this.commentsStream.write(commentsXmlStream.xml);\n\n    const vmlShapeXform = new VmlShapeXform();\n    const vmlXmlStream = new XmlStream();\n    vmlShapeXform.render(vmlXmlStream, comment, index);\n    this.vmlStream.write(vmlXmlStream.xml);\n  }\n\n  _writeClose() {\n    this.commentsStream.write('</commentList></comments>');\n    this.vmlStream.write('</xml>');\n  }\n\n  addComments(comments) {\n    if (comments && comments.length) {\n      if (!this.startedData) {\n        this._worksheet.comments = [];\n        this._writeOpen();\n        this._addRelationships();\n        this._addCommentRefs();\n        this.startedData = true;\n      }\n\n      comments.forEach(item => {\n        item.refAddress = colCache.decodeAddress(item.ref);\n      });\n\n      comments.forEach(comment => {\n        this._writeComment(comment, this.count);\n        this.count += 1;\n      });\n    }\n  }\n\n  commit() {\n    if (this.count) {\n      this._writeClose();\n      this.commentsStream.end();\n      this.vmlStream.end();\n    }\n  }\n}\n\nmodule.exports = SheetCommentsWriter;\n"], "mappings": ";;AAAA,MAAMA,SAAS,GAAGC,OAAO,CAAC,wBAAwB,CAAC;AACnD,MAAMC,OAAO,GAAGD,OAAO,CAAC,qBAAqB,CAAC;AAC9C,MAAME,QAAQ,GAAGF,OAAO,CAAC,uBAAuB,CAAC;AACjD,MAAMG,YAAY,GAAGH,OAAO,CAAC,wCAAwC,CAAC;AACtE,MAAMI,aAAa,GAAGJ,OAAO,CAAC,0CAA0C,CAAC;AAEzE,MAAMK,mBAAmB,CAAC;EACxBC,WAAWA,CAACC,SAAS,EAAEC,eAAe,EAAEC,OAAO,EAAE;IAC/C;IACA,IAAI,CAACC,EAAE,GAAGD,OAAO,CAACC,EAAE;IACpB,IAAI,CAACC,KAAK,GAAG,CAAC;IACd,IAAI,CAACC,UAAU,GAAGL,SAAS;IAC3B,IAAI,CAACM,SAAS,GAAGJ,OAAO,CAACK,QAAQ;IACjC,IAAI,CAACC,gBAAgB,GAAGP,eAAe;EACzC;EAEA,IAAIQ,cAAcA,CAAA,EAAG;IACnB,IAAI,CAAC,IAAI,CAACC,eAAe,EAAE;MACzB;MACA,IAAI,CAACA,eAAe,GAAG,IAAI,CAACJ,SAAS,CAACK,WAAW,CAAE,eAAc,IAAI,CAACR,EAAG,MAAK,CAAC;IACjF;IACA,OAAO,IAAI,CAACO,eAAe;EAC7B;EAEA,IAAIE,SAASA,CAAA,EAAG;IACd,IAAI,CAAC,IAAI,CAACC,UAAU,EAAE;MACpB;MACA,IAAI,CAACA,UAAU,GAAG,IAAI,CAACP,SAAS,CAACK,WAAW,CAAE,yBAAwB,IAAI,CAACR,EAAG,MAAK,CAAC;IACtF;IACA,OAAO,IAAI,CAACU,UAAU;EACxB;EAEAC,iBAAiBA,CAAA,EAAG;IAClB,MAAMC,UAAU,GAAG;MACjBC,IAAI,EAAEtB,OAAO,CAACuB,QAAQ;MACtBC,MAAM,EAAG,cAAa,IAAI,CAACf,EAAG;IAChC,CAAC;IACD,IAAI,CAACK,gBAAgB,CAACW,eAAe,CAACJ,UAAU,CAAC;IAEjD,MAAMK,aAAa,GAAG;MACpBJ,IAAI,EAAEtB,OAAO,CAAC2B,UAAU;MACxBH,MAAM,EAAG,yBAAwB,IAAI,CAACf,EAAG;IAC3C,CAAC;IACD,IAAI,CAACmB,QAAQ,GAAG,IAAI,CAACd,gBAAgB,CAACW,eAAe,CAACC,aAAa,CAAC;EACtE;EAEAG,eAAeA,CAAA,EAAG;IAChB,IAAI,CAACjB,SAAS,CAACkB,WAAW,CAACC,IAAI,CAAC;MAC9BC,WAAW,EAAG,WAAU,IAAI,CAACvB,EAAG,EAAC;MACjCwB,UAAU,EAAG,aAAY,IAAI,CAACxB,EAAG;IACnC,CAAC,CAAC;EACJ;EAEAyB,UAAUA,CAAA,EAAG;IACX,IAAI,CAACnB,cAAc,CAACoB,KAAK,CACvB,yDAAyD,GACvD,8EAA8E,GAC9E,4CAA4C,GAC5C,eACJ,CAAC;IACD,IAAI,CAACjB,SAAS,CAACiB,KAAK,CAClB,wCAAwC,GACtC,kJAAkJ,GAClJ,8BAA8B,GAC9B,mCAAmC,GACnC,kBAAkB,GAClB,qGAAqG,GACrG,gCAAgC,GAChC,qDAAqD,GACrD,gBACJ,CAAC;EACH;EAEAC,aAAaA,CAACC,OAAO,EAAEC,KAAK,EAAE;IAC5B,MAAMC,YAAY,GAAG,IAAIrC,YAAY,CAAC,CAAC;IACvC,MAAMsC,iBAAiB,GAAG,IAAI1C,SAAS,CAAC,CAAC;IACzCyC,YAAY,CAACE,MAAM,CAACD,iBAAiB,EAAEH,OAAO,CAAC;IAC/C,IAAI,CAACtB,cAAc,CAACoB,KAAK,CAACK,iBAAiB,CAACE,GAAG,CAAC;IAEhD,MAAMC,aAAa,GAAG,IAAIxC,aAAa,CAAC,CAAC;IACzC,MAAMyC,YAAY,GAAG,IAAI9C,SAAS,CAAC,CAAC;IACpC6C,aAAa,CAACF,MAAM,CAACG,YAAY,EAAEP,OAAO,EAAEC,KAAK,CAAC;IAClD,IAAI,CAACpB,SAAS,CAACiB,KAAK,CAACS,YAAY,CAACF,GAAG,CAAC;EACxC;EAEAG,WAAWA,CAAA,EAAG;IACZ,IAAI,CAAC9B,cAAc,CAACoB,KAAK,CAAC,2BAA2B,CAAC;IACtD,IAAI,CAACjB,SAAS,CAACiB,KAAK,CAAC,QAAQ,CAAC;EAChC;EAEAW,WAAWA,CAACC,QAAQ,EAAE;IACpB,IAAIA,QAAQ,IAAIA,QAAQ,CAACC,MAAM,EAAE;MAC/B,IAAI,CAAC,IAAI,CAACC,WAAW,EAAE;QACrB,IAAI,CAACtC,UAAU,CAACoC,QAAQ,GAAG,EAAE;QAC7B,IAAI,CAACb,UAAU,CAAC,CAAC;QACjB,IAAI,CAACd,iBAAiB,CAAC,CAAC;QACxB,IAAI,CAACS,eAAe,CAAC,CAAC;QACtB,IAAI,CAACoB,WAAW,GAAG,IAAI;MACzB;MAEAF,QAAQ,CAACG,OAAO,CAACC,IAAI,IAAI;QACvBA,IAAI,CAACC,UAAU,GAAGnD,QAAQ,CAACoD,aAAa,CAACF,IAAI,CAACG,GAAG,CAAC;MACpD,CAAC,CAAC;MAEFP,QAAQ,CAACG,OAAO,CAACb,OAAO,IAAI;QAC1B,IAAI,CAACD,aAAa,CAACC,OAAO,EAAE,IAAI,CAAC3B,KAAK,CAAC;QACvC,IAAI,CAACA,KAAK,IAAI,CAAC;MACjB,CAAC,CAAC;IACJ;EACF;EAEA6C,MAAMA,CAAA,EAAG;IACP,IAAI,IAAI,CAAC7C,KAAK,EAAE;MACd,IAAI,CAACmC,WAAW,CAAC,CAAC;MAClB,IAAI,CAAC9B,cAAc,CAACyC,GAAG,CAAC,CAAC;MACzB,IAAI,CAACtC,SAAS,CAACsC,GAAG,CAAC,CAAC;IACtB;EACF;AACF;AAEAC,MAAM,CAACC,OAAO,GAAGtD,mBAAmB"}